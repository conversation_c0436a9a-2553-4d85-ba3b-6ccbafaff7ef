package csv

import (
	"strings"
	"testing"
)

func TestParseWithHeader(t *testing.T) {
	csvData := `name,age,city
John,25,NYC
Jane,30,LA
Bob,35,Chicago`

	parser := NewParser()
	parser.HasHeader = true

	data, err := parser.Parse(strings.NewReader(csvData))
	if err != nil {
		t.Fatalf("Parse failed: %v", err)
	}

	// Check headers
	expectedHeaders := []string{"name", "age", "city"}
	if len(data.Headers) != len(expectedHeaders) {
		t.Fatalf("Expected %d headers, got %d", len(expectedHeaders), len(data.Headers))
	}
	for i, expected := range expectedHeaders {
		if data.Headers[i] != expected {
			t.<PERSON><PERSON><PERSON>("Header %d: expected %s, got %s", i, expected, data.Headers[i])
		}
	}

	// Check dimensions
	if data.NumColumns != 3 {
		t.<PERSON><PERSON><PERSON>("Expected 3 columns, got %d", data.NumColumns)
	}
	if data.NumRows != 3 {
		t.<PERSON><PERSON><PERSON>("Expected 3 rows, got %d", data.NumRows)
	}


}

func TestParseWithoutHeader(t *testing.T) {
	csvData := `<PERSON>,25,NYC
Jane,30,LA
Bob,35,Chicago`

	parser := NewParser()
	parser.HasHeader = false

	data, err := parser.Parse(strings.NewReader(csvData))
	if err != nil {
		t.Fatalf("Parse failed: %v", err)
	}

	// Check no headers
	if len(data.Headers) != 0 {
		t.Errorf("Expected no headers, got %d", len(data.Headers))
	}

	// Check dimensions
	if data.NumColumns != 3 {
		t.Errorf("Expected 3 columns, got %d", data.NumColumns)
	}
	if data.NumRows != 3 {
		t.Errorf("Expected 3 rows, got %d", data.NumRows)
	}

	
	
}

func TestParseEmpty(t *testing.T) {
	csvData := ``

	parser := NewParser()
	data, err := parser.Parse(strings.NewReader(csvData))
	if err != nil {
		t.Fatalf("Parse failed: %v", err)
	}

	if data.NumColumns != 0 {
		t.Errorf("Expected 0 columns, got %d", data.NumColumns)
	}
	if data.NumRows != 0 {
		t.Errorf("Expected 0 rows, got %d", data.NumRows)
	}
}
