package csv

import (
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"os"
)

// ParsedData holds the raw CSV data in an efficient structure
// to avoid unnecessary copying of data throughout the system
type TrainingData struct {
	// Headers contains the column names from the first row (if hasHeader is true)
	Headers []string

	// Rows contains all data rows as string slices
	// Each row corresponds to one record from the CSV
	Records [][]string

	// NumColumns is the number of columns in the dataset
	NumColumns int

	// NumRows is the number of data rows (excluding header if present)
	NumRows int
}

// Parser handles CSV file parsing with configurable options
type Parser struct {
	// Delimiter is the field delimiter (default: comma)
	Delimiter rune

	// HasHeader indicates if the first row contains column headers
	HasHeader bool

	// TrimLeadingSpace removes leading whitespace from fields
	TrimLeadingSpace bool
}

// NewParser creates a new CSV parser with default settings
func NewParser() *Parser {
	return &Parser{
		Delimiter: ',',
	}
}

// ParseFile parses a CSV file and returns the structured data
func (p *Parser) ParseCsvFile(filepath string) (*TrainingData, error) {
	file, err := os.Open(filepath)
	if err != nil {
		return nil, fmt.Errorf("failed to open file %s: %w", filepath, err)
	}
	defer file.Close()

	return p.Parse(file)
}

// Parse parses CSV data from an io.Reader and returns the structured data
func (p *Parser) Parse(reader io.Reader) (*TrainingData, error) {
	csvReader := csv.NewReader(reader)
	csvReader.Comma = p.Delimiter

	// Read all records at once
	allRecords, err := csvReader.ReadAll()
	if err != nil {
		return nil, fmt.Errorf("failed to parse CSV: %w", err)
	}

	if len(allRecords) == 0 {
		return &TrainingData{
			Headers:    []string{},
			Records:    [][]string{},
			NumColumns: 0,
			NumRows:    0,
		}, errors.New("no record found")
	}

	var headers []string
	var records [][]string
	numColumns := len(allRecords[0])

	if len(allRecords) < 1 {
		return nil, fmt.Errorf("CSV has header flag set but no data rows")
	}
	headers = allRecords[0]
	records = allRecords[1:]

	// Validate that all rows have the same number of columns
	for i, row := range records {
		if len(row) != numColumns {
			return nil, fmt.Errorf("row %d has %d columns, expected %d", i, len(row), numColumns)
		}
	}

	return &TrainingData{
		Headers:    headers,
		Records:    records,
		NumColumns: numColumns,
		NumRows:    len(records),
	}, nil
}
