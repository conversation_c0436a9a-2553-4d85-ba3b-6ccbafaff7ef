package dataset

import (
	"github.com/berrijam/mulberri/internal/io/formats/csv"
)

// Loader handles loading CSV data and basic type conversion
type Loader struct {
	csvParser *csv.Parser
}

// NewLoader creates a new dataset loader
func NewLoader() *Loader {
	return &Loader{
		csvParser: csv.NewParser(),
	}
}

// LoadCSV loads a CSV file and returns the parsed data
func (l *Loader) LoadCSV(csvPath string) (*csv.TrainingData, error) {
	return l.csvParser.ParseCsvFile(csvPath)
}
