// Package dataset provides functionality for loading and managing datasets
// from various file formats. It serves as a domain-specific layer that
// builds upon the raw format parsers to provide higher-level dataset operations.
package dataset

import (
	"github.com/berrijam/mulberri/internal/io/formats/csv"
)

// Loader handles loading CSV data and basic type conversion.
// It provides a domain-specific interface for dataset loading operations
// while delegating the actual parsing to format-specific parsers.
//
// The Loader follows the separation of concerns principle where:
// - Format layer (io/formats/) handles raw parsing
// - Loader layer (data/*/loader.go) provides domain-specific loading
// - Future converter layer will handle type conversion
// - Future validation layer will handle business rules
type Loader struct {
	// csvParser handles the actual CSV file parsing operations
	csvParser *csv.Parser
}

// NewLoader creates a new dataset loader with default CSV parser settings.
// The returned loader is ready to use for loading CSV files.
//
// Returns:
//   - *Loader: A new loader instance configured with default settings
//
// Example:
//
//	loader := NewLoader()
//	data, err := loader.LoadCSV("data.csv")
func NewLoader() *Loader {
	return &Loader{
		csvParser: csv.NewParser(),
	}
}

// LoadCSV loads a CSV file and returns the parsed data as TrainingData.
// The first row of the CSV is always treated as headers, and subsequent
// rows are treated as data records.
//
// Parameters:
//   - csvPath: The file path to the CSV file to load
//
// Returns:
//   - *csv.TrainingData: The parsed CSV data containing headers and records
//   - error: An error if the file cannot be opened or parsed
//
// The returned TrainingData contains:
//   - Headers: Column names from the first row
//   - Records: Data rows as string slices
//   - NumColumns: Number of columns in the dataset
//   - NumRows: Number of data rows (excluding header)
//
// Example:
//
//	loader := NewLoader()
//	data, err := loader.LoadCSV("training_data.csv")
//	if err != nil {
//	    log.Fatal(err)
//	}
//	fmt.Printf("Loaded %d rows with %d columns\n", data.NumRows, data.NumColumns)
//
// Errors:
//   - File not found or permission errors
//   - CSV parsing errors (malformed CSV, inconsistent column counts)
//   - Empty file errors
func (l *Loader) LoadCSV(csvPath string) (*csv.TrainingData, error) {
	return l.csvParser.ParseCsvFile(csvPath)
}
